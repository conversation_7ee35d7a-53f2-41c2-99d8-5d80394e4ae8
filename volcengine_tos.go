package main

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/volcengine/ve-tos-golang-sdk/v2/tos"
)

// 缓存的TOS凭证
var (
	cachedTOSCredentials *TOSCredentials
	tosCredentialsMutex  sync.RWMutex
)

// UploadImageToTOS 将base64图片上传到火山TOS并生成临时鉴权URL
// imageBase64: 图片的base64编码字符串，格式为"data:image/{format};base64,{base64Data}"
// 返回: 临时鉴权URL, 错误
func UploadImageToTOS(ctx context.Context, imageBase64 string) (string, error) {
	// 1. 获取TOS临时凭证
	if TencentCloudHttpClient == nil {
		return "", fmt.Errorf("腾讯云客户端未初始化")
	}
	// 检查缓存的凭证是否有效
	var credentials *TOSCredentials
	var err error
	tosCredentialsMutex.RLock()
	if cachedTOSCredentials != nil {
		// 检查凭证是否过期（提前5分钟刷新，避免临界点问题）
		if time.Now().Add(5 * time.Minute).Before(cachedTOSCredentials.Expiration) {
			credentials = cachedTOSCredentials
			log.Printf("使用缓存的TOS临时凭证，过期时间: %v", cachedTOSCredentials.Expiration)
		}
	}
	tosCredentialsMutex.RUnlock()
	// 如果没有有效的缓存凭证，则获取新凭证
	if credentials == nil {
		credentials, err = TencentCloudHttpClient.GetTOSCredentials(ctx)
		if err != nil {
			return "", fmt.Errorf("获取TOS临时凭证失败: %v", err)
		}
		// 更新缓存
		tosCredentialsMutex.Lock()
		cachedTOSCredentials = credentials
		tosCredentialsMutex.Unlock()
		log.Printf("获取新的TOS临时凭证成功，过期时间: %v", credentials.Expiration)
	}
	// 2. 解析base64图片数据
	// 格式: "data:image/{format};base64,{base64Data}"
	parts := strings.Split(imageBase64, ",")
	if len(parts) != 2 {
		return "", fmt.Errorf("无效的base64图片格式")
	}
	// 获取图片格式
	formatParts := strings.Split(parts[0], "/")
	if len(formatParts) != 2 {
		return "", fmt.Errorf("无法识别图片格式")
	}
	formatWithSemicolon := strings.Split(formatParts[1], ";")
	if len(formatWithSemicolon) != 2 {
		return "", fmt.Errorf("无法识别图片格式")
	}
	imageFormat := formatWithSemicolon[0] // 如 "png", "jpeg"
	// 解码base64数据
	imageData, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return "", fmt.Errorf("解码base64图片数据失败: %v", err)
	}
	// 3. 生成唯一的文件名
	timestamp := time.Now().UnixNano()
	randomBytes := make([]byte, 8)
	rand.Read(randomBytes)
	randomStr := hex.EncodeToString(randomBytes)
	fileName := fmt.Sprintf("aig_%d_%s.%s", timestamp, randomStr, imageFormat)
	// 4. 设置TOS客户端配置
	// 从凭证中获取bucket和endpoint，如果没有则使用默认值
	bucketName := credentials.Bucket
	if bucketName == "" {
		bucketName = "aig-bucket" // 默认值
	}
	objectKey := "images/" + fileName
	endpoint := credentials.Endpoint
	if endpoint == "" {
		endpoint = "tos-cn-beijing.volces.com"
	}
	intranetEndpoint := credentials.IntranetEndpoint
	if intranetEndpoint == "" {
		intranetEndpoint = "tos-cn-beijing.ivolces.com"
	}
	region := credentials.Region
	// 5. 创建TOS客户端
	cred := tos.NewStaticCredentials(credentials.AccessKeyID, credentials.SecretAccessKey)
	cred.WithSecurityToken(credentials.SessionToken)
	client, err := tos.NewClientV2(endpoint,
		tos.WithRegion(region),
		tos.WithCredentials(cred),
	)
	if err != nil {
		return "", fmt.Errorf("创建TOS客户端失败: %v", err)
	}
	// 校验码
	hash := md5.New()
	hash.Write(imageData)
	contentMD5 := base64.StdEncoding.EncodeToString(hash.Sum(nil))
	// 6. 上传图片
	reader := strings.NewReader(string(imageData))
	input := &tos.PutObjectV2Input{
		PutObjectBasicInput: tos.PutObjectBasicInput{
			Bucket:     bucketName,
			Key:        objectKey,
			ContentMD5: contentMD5,
		},
		Content: reader,
	}
	// 设置Content-Type和Content-Length
	input.ContentType = "image/" + imageFormat
	input.ContentLength = int64(len(imageData))
	_, err = client.PutObjectV2(context.Background(), input)
	if err != nil {
		return "", fmt.Errorf("上传图片失败: %v", err)
	}
	// 7. 生成临时鉴权URL（有效期5分钟）
	// 使用SDK生成预签名URL
	preSignedURLInput := &tos.PreSignedURLInput{
		HTTPMethod:          http.MethodGet,
		Bucket:              bucketName,
		Key:                 objectKey,
		Expires:             300,
		AlternativeEndpoint: intranetEndpoint,
	}
	preSignedURL, err := client.PreSignedURL(preSignedURLInput)
	if err != nil {
		return "", fmt.Errorf("生成临时鉴权URL失败: %v", err)
	}
	log.Printf("生成临时鉴权URL成功: %s", preSignedURL.SignedUrl)
	return preSignedURL.SignedUrl, nil
}
