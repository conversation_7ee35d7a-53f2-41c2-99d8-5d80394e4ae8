package main

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

// ExportGradingRecordsToCSV 将阅卷记录导出为CSV文件
// exportDir: 导出目录，如果为空则显示目录选择对话框
// 返回: 错误信息
func (a *App) ExportGradingRecordsToCSV(exportDir string) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取所有记录
	result := GetGradingRecords(db)
	if result.Error != "" {
		return fmt.Errorf("获取阅卷记录失败: %s", result.Error)
	}

	if len(result.Records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间）
	timestamp := time.Now().Format("20060102-150405")
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s.csv", timestamp))

	// 创建CSV文件
	file, err := os.Create(exportFilePath)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %v", err)
	}
	defer file.Close()

	// 添加UTF-8 BOM，以便Excel正确识别中文
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return fmt.Errorf("写入UTF-8 BOM失败: %v", err)
	}

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 设置CSV分隔符为逗号
	writer.Comma = ','

	// 写入标题行
	headers := []string{
		"创建时间",
		"用户",
		"分数",
		"评分标准",
		"学生答案",
		"评分细节",
	}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV标题行失败: %v", err)
	}

	// 写入数据行
	for _, record := range result.Records {
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")
		score := strconv.FormatFloat(record.Score, 'f', 1, 64)

		row := []string{
			createdTime,
			record.UserEmail,
			score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV数据行失败: %v", err)
		}
	}

	// 确保所有数据都写入文件
	writer.Flush()
	if err := writer.Error(); err != nil {
		return fmt.Errorf("刷新CSV写入器失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// ExportGradingRecordsToCSVByTimeRange 将指定时间范围内的阅卷记录导出为CSV文件
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// 返回: 错误信息
func (a *App) ExportGradingRecordsToCSVByTimeRange(exportDir string, startTime, endTime time.Time) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取指定时间范围内的记录
	records := GetGradingRecordsByTimeRange(db, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围内没有阅卷记录可导出")
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间和时间范围）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s.csv", timeRangeStr, timestamp))

	// 创建CSV文件
	file, err := os.Create(exportFilePath)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %v", err)
	}
	defer file.Close()

	// 添加UTF-8 BOM，以便Excel正确识别中文
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return fmt.Errorf("写入UTF-8 BOM失败: %v", err)
	}

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 设置CSV分隔符为逗号
	writer.Comma = ','

	// 写入标题行
	headers := []string{
		"创建时间",
		"用户",
		"分数",
		"评分标准",
		"学生答案",
		"评分细节",
	}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV标题行失败: %v", err)
	}

	// 写入数据行
	for _, record := range records {
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")
		score := strconv.FormatFloat(record.Score, 'f', 1, 64)

		row := []string{
			createdTime,
			record.UserEmail,
			score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV数据行失败: %v", err)
		}
	}

	// 确保所有数据都写入文件
	writer.Flush()
	if err := writer.Error(); err != nil {
		return fmt.Errorf("刷新CSV写入器失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}

// ExportGradingRecordsToCSVByTimeRangeAndCriteria 将指定时间范围和评分标准内的阅卷记录导出为CSV文件
// exportDir: 导出目录，如果为空则显示目录选择对话框
// startTime, endTime: 时间范围
// criteriaID: 评分标准ID
// 返回: 错误信息
func (a *App) ExportGradingRecordsToCSVByTimeRangeAndCriteria(exportDir string, startTime, endTime time.Time, criteriaID string) error {
	if a.ctx == nil {
		return fmt.Errorf("应用上下文未初始化")
	}

	// 通知前端开始加载
	a.StartLoading()
	defer a.StopLoading()

	// 获取所有阅卷记录
	db := GetDB()
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 获取指定时间范围和评分标准的记录
	records := GetGradingRecordsByTimeRangeAndCriteria(db, criteriaID, startTime, endTime)
	if len(records) == 0 {
		// 通知前端没有记录可导出
		rt.EventsEmit(a.ctx, "export:noRecords")
		return fmt.Errorf("指定时间范围和评分标准内没有阅卷记录可导出")
	}

	// 获取评分标准内容
	var criteriaContent string
	var criteria GradingCriteria
	if err := db.Where("id = ?", criteriaID).First(&criteria).Error; err == nil {
		criteriaContent = criteria.Content
	} else {
		criteriaContent = "未知标准"
	}

	// 如果没有指定导出目录，则显示目录选择对话框
	if exportDir == "" {
		var err error
		exportDir, err = a.SelectExportDirectory()
		if err != nil {
			return fmt.Errorf("选择导出目录失败: %v", err)
		}

		// 如果用户取消选择，使用默认目录
		if exportDir == "" {
			exportDir = a.GetDefaultExportDirectory()
		}
	}

	// 创建导出目录（如果不存在）
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建导出文件名（使用当前时间、时间范围和评分标准）
	timestamp := time.Now().Format("20060102-150405")
	timeRangeStr := fmt.Sprintf("%s至%s", startTime.Format("0102-1504"), endTime.Format("0102-1504"))
	criteriaShort := criteriaContent
	if len(criteriaShort) > 20 {
		criteriaShort = criteriaShort[:20] + "..."
	}
	exportFilePath := filepath.Join(exportDir, fmt.Sprintf("阅卷记录-%s-%s-%s.csv", timeRangeStr, criteriaShort, timestamp))

	// 创建CSV文件
	file, err := os.Create(exportFilePath)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %v", err)
	}
	defer file.Close()

	// 添加UTF-8 BOM，以便Excel正确识别中文
	_, err = file.Write([]byte{0xEF, 0xBB, 0xBF})
	if err != nil {
		return fmt.Errorf("写入UTF-8 BOM失败: %v", err)
	}

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 设置CSV分隔符为逗号
	writer.Comma = ','

	// 写入标题行
	headers := []string{
		"创建时间",
		"用户",
		"分数",
		"评分标准",
		"学生答案",
		"评分细节",
	}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV标题行失败: %v", err)
	}

	// 写入数据行
	for _, record := range records {
		createdTime := time.Time(record.CreatedAt).Format("2006-01-02 15:04:05")
		score := strconv.FormatFloat(record.Score, 'f', 1, 64)

		row := []string{
			createdTime,
			record.UserEmail,
			score,
			record.GradingCriteria,
			record.AnswerText,
			record.ScoreDetails,
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV数据行失败: %v", err)
		}
	}

	// 确保所有数据都写入文件
	writer.Flush()
	if err := writer.Error(); err != nil {
		return fmt.Errorf("刷新CSV写入器失败: %v", err)
	}

	// 通知前端导出成功
	rt.EventsEmit(a.ctx, "export:success", exportFilePath, exportDir)

	// 打开导出目录
	rt.BrowserOpenURL(a.ctx, fmt.Sprintf("file://%s", exportDir))

	return nil
}
