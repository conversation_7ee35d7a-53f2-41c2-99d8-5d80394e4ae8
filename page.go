package main

import (
	"fmt"
)

// FindLoginButton 查找登录按钮
// 返回: (是否可见, 错误)
func (a *App) FindLoginButton() (bool, error) {
	if a.GetTargetPage() == nil {
		return false, fmt.Errorf("page not initialized")
	}

	// 使用Playwright定位器查找包含"登录"文本的按钮
	visible, err := a.GetTargetPage().Locator("button:has-text('登录')").First().IsVisible()
	if err != nil {
		return false, fmt.Errorf("failed to find login button: %v", err)
	}
	return visible, nil
}

// ClickButton 点击指定选择器的按钮
// selector: CSS选择器或文本选择器
// 返回: 操作过程中遇到的错误
func (a *App) ClickButton(selector string) error {
	if a.GetTargetPage() == nil {
		return fmt.Errorf("page not initialized")
	}

	err := a.GetTargetPage().Locator(selector).First().Click()
	if err != nil {
		return fmt.Errorf("failed to click button: %v", err)
	}
	return nil
}

// FindButton 查找指定选择器的按钮
// selector: CSS选择器或文本选择器
// 返回: (按钮是否存在, 错误)
func (a *App) FindButton(selector string) (bool, error) {
	if a.GetTargetPage() == nil {
		return false, fmt.Errorf("page not initialized")
	}

	exists, err := a.GetTargetPage().Locator(selector).First().IsVisible()
	if err != nil {
		return false, fmt.Errorf("failed to find button: %v", err)
	}
	return exists, nil
}

func (a *App) FetchImage(urlName string) (string, error) {
	if a.GetTargetPage() == nil {
		return "", fmt.Errorf("page not initialized")
	}
	// 根据传入的url判断抓图位置
	switch urlName {
	case GUANGDA:
		// 获取span元素的文本内容作为key
		selector := "body > div.app-wrap > div.page > div.header_wrap > div > section.f > div.jl > span"
		locator := a.GetTargetPage().Locator(selector).First()
		if locator == nil {
			return "", fmt.Errorf("element not found: %s", selector)
		}
		// 检查元素是否可见
		visible, err := locator.IsVisible()
		if err != nil || !visible {
			return "", fmt.Errorf("element not visible: %s, error: %v", selector, err)
		}
		// 获取元素的文本内容
		key, err := locator.TextContent()
		if err != nil {
			return "", fmt.Errorf("failed to get element text content: %v", err)
		}
		if key == "" {
			return "", fmt.Errorf("element text content is empty")
		}
		if AppLogger != nil {
			AppLogger.Debug("获取到span元素文本内容: %s", key)
		}
		// 从imageUrlMap获取图片URL
		imageURL, ok := imageUrlMap[key]
		if !ok {
			return "", fmt.Errorf("image URL not found for key: %s", key)
		}
		if AppLogger != nil {
			AppLogger.Debug("获取到图片URL: %s", imageURL)
		}
		// 下载图片并获取base64编码
		return downloadImage(imageURL, 800)
	default:
		// 默认处理，从配置中获取
		config := GetGlobalConfig()
		var targetConfig *ConfigItem
		for _, item := range config {
			if item.ID == urlName {
				targetConfig = &item
				break
			}
		}
		if targetConfig != nil {
			for _, action := range targetConfig.Actions {
				if action.Type == "screenshot" {
					if AppLogger != nil {
						AppLogger.Debug("执行截图操作: %s", action.Selector)
					}
					return a.ScreenshotElement(action.Selector, 800)
				}
			}
		}
		return "", fmt.Errorf("unknown url: %s", urlName)
	}
}

// 提交分数
func (a *App) FillGrade(urlName, grade string) error {
	// 获取配置
	config := GetGlobalConfig()
	var targetConfig *ConfigItem
	for _, item := range config {
		if item.ID == urlName {
			targetConfig = &item
			break
		}
	}
	if targetConfig == nil {
		return fmt.Errorf("未找到配置ID: %s", urlName)
	}
	if len(targetConfig.Actions) == 0 {
		return fmt.Errorf("配置ID %s 没有定义任何操作", urlName)
	}
	// 按照Index排序Actions
	sortedActions := make([]Action, len(targetConfig.Actions))
	copy(sortedActions, targetConfig.Actions)
	// 使用冒泡排序按Index排序
	for i := range sortedActions[:len(sortedActions)-1] {
		for j := range sortedActions[:len(sortedActions)-i-1] {
			if sortedActions[j].Index > sortedActions[j+1].Index {
				sortedActions[j], sortedActions[j+1] = sortedActions[j+1], sortedActions[j]
			}
		}
	}
	for _, action := range sortedActions {
		// 跳过截图和抓取操作
		if action.Type == "screenshot" || action.Type == "fetch" {
			continue
		}
		if AppLogger != nil {
			AppLogger.Debug("执行操作 %d: %s %s", action.Index, action.Type, action.Selector)
		}
		switch action.Type {
		case "click":
			if err := a.ClickElement(action.Selector); err != nil {
				return fmt.Errorf("点击元素失败: %v", err)
			}
		case "fill":
			if err := a.FillElement(action.Selector, grade); err != nil {
				return fmt.Errorf("填充元素失败: %v", err)
			}
		default:
			if AppLogger != nil {
				AppLogger.Warning("未知操作类型: %s，跳过", action.Type)
			}
		}
	}
	if AppLogger != nil {
		AppLogger.Info("提交评分完成，分数: %s", grade)
	}
	return nil
}
