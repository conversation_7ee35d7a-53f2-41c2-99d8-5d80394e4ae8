package main

import (
	"fmt"
	"strings"
)

// GradingCriteriaStruct 结构化评分标准
type GradingCriteriaStruct struct {
	DefaultCriteria string `json:"default_criteria" yaml:"default_criteria" mapstructure:"default_criteria"` // 默认评分标准
	ScoringPoints   string `json:"scoring_points" yaml:"scoring_points" mapstructure:"scoring_points"`       // 得分点
	DeductionPoints string `json:"deduction_points" yaml:"deduction_points" mapstructure:"deduction_points"` // 不得分点
	TotalScore      int    `json:"total_score" yaml:"total_score" mapstructure:"total_score"`                // 总分
}

// ToPromptText 将结构化评分标准转换为发送给腾讯云的文本格式
func (g GradingCriteriaStruct) ToPromptText() string {
	var parts []string
	// 添加默认评分标准
	if g.DefaultCriteria != "" {
		parts = append(parts, "评分标准:\n"+g.DefaultCriteria)
	}
	// 添加得分点
	if g.ScoringPoints != "" {
		parts = append(parts, "得分点:\n"+g.ScoringPoints)
	}
	// 添加不得分点
	if g.DeductionPoints != "" {
		parts = append(parts, "不得分点:\n"+g.DeductionPoints)
	}
	// 添加总分
	if g.TotalScore != 0 {
		parts = append(parts, "总分:"+fmt.Sprint(g.TotalScore))
	}
	// 使用双换行符连接各部分
	return strings.Join(parts, "\n\n")
}

// GradingResponse 结构体用于描述评分响应的结构
// 包含三个字段：
// - student_answer: 从图片中提取并整理的学生答案关键内容概要
// - score: 计算得到的最终数字得分
// - grading_details: 根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容
type GradingResponse struct {
	StudentAnswer  string `json:"student_answer"`
	Score          int64  `json:"score"`
	GradingDetails string `json:"grading_details"`
	Balance        int    `json:"balance,omitempty"`
}

// 保留旧结构体定义以便向后兼容
// Input 结构体用于描述输入的结构
// 包含两个字段：
// - GradingCriteria: 评分标准
// - StudentAnswer: 学生回答
type Input struct {
	GradingCriteria string `json:"grading_criteria"`
	StudentAnswer   string `json:"student_answer"`
}

// Output 结构体用于描述输出的结构
// 包含两个字段：
// - GradingDetails: 评分细节
// - Score: 分数
type Output struct {
	GradingDetails string `json:"grading_details"`
	Score          int64  `json:"score"`
}

// ChatResponse 结构体用于描述API响应的结构
// 包含以下字段：
// - ID: 响应ID
// - Content: 响应内容
// - Usage: 使用情况
// - Cost: API调用花费(元)
type ChatResponse struct {
	ID        string `json:"id"`
	OcrResult string `json:"ocr_result"`
	Content   string `json:"content"`
	Usage     Usage  `json:"usage"`
	Cost      int    `json:"cost" gorm:"type:integer"`
	Balance   int    `json:"balance" gorm:"type:integer"`
}

// Usage 结构体用于描述API调用的使用情况，包括生成的令牌数量、使用的令牌数量和总令牌数量。
// 它包含以下字段：
// - CompletionTokens: 生成的令牌数量。
// - CompletionTokensDetails: 生成的令牌详细信息，包括原因令牌数量。
// - PromptTokens: 使用的令牌数量。
// - PromptTokensDetails: 使用的令牌详细信息，包括缓存令牌数量。
// - TotalTokens: 总令牌数量。
type Usage struct {
	CompletionTokens        int64                   `json:"completion_tokens"`
	CompletionTokensDetails CompletionTokensDetails `json:"completion_tokens_details"`
	PromptTokens            int64                   `json:"prompt_tokens"`
	PromptTokensDetails     PromptTokensDetails     `json:"prompt_tokens_details"`
	TotalTokens             int64                   `json:"total_tokens"`
}

type CompletionTokensDetails struct {
	ReasoningTokens int64 `json:"reasoning_tokens"`
}

type PromptTokensDetails struct {
	CachedTokens int64 `json:"cached_tokens"`
}
