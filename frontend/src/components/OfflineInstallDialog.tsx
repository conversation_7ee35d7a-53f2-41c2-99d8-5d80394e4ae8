import React, { useEffect, useState } from "react";
import { Modal, Typography, Space, Button, Alert } from "antd";
import { BrowserOpenURL } from "../../wailsjs/runtime";
import {
  InstallComponentsOffline,
  GetComponentDownloadLink,
} from "../../wailsjs/go/main/App";
import { LogInfo, LogError } from "../../wailsjs/runtime/runtime";

const { Paragraph, Text, Link } = Typography;

interface OfflineInstallDialogProps {
  visible: boolean;
  onClose: () => void;
}

const OfflineInstallDialog: React.FC<OfflineInstallDialogProps> = ({
  visible,
  onClose,
}) => {
  const [installing, setInstalling] = React.useState(false);
  const [downloadLink, setDownloadLink] = useState<string>();
  const [errorMessage, setErrorMessage] = useState<string>("");

  // 获取下载链接
  useEffect(() => {
    if (visible) {
      const fetchDownloadLinks = async () => {
        try {
          const link = await GetComponentDownloadLink();
          setDownloadLink(link);
        } catch (error) {
          LogError(`获取下载链接失败: ${error}`);
        }
      };
      fetchDownloadLinks();
      // 重置错误信息
      setErrorMessage("");
    }
  }, [visible]);

  const handleInstall = async () => {
    try {
      setInstalling(true);
      setErrorMessage(""); // 清除之前的错误信息
      LogInfo("开始离线安装组件");
      await InstallComponentsOffline();
      LogInfo("组件离线安装成功");
      setInstalling(false);
      // 显示安装成功的提示对话框
      Modal.success({
        title: "安装成功",
        content: "必要组件已成功安装，欢迎使用",
        onOk: onClose
      });
    } catch (error) {
      const errorStr = String(error);
      LogError(`离线安装组件失败: ${errorStr}`);
      setErrorMessage(errorStr);
      setInstalling(false);
    }
  };

  const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (downloadLink) {
      BrowserOpenURL(downloadLink);
    }
  };

  return (
    <Modal
      title="离线安装必要组件"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
      maskClosable={false}
      closable={!installing}
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Paragraph>您可以通过离线方式安装山竹阅卷所需的必要组件。</Paragraph>

        {errorMessage && (
          <Alert
            message="安装失败"
            description={errorMessage}
            type="error"
            showIcon
          />
        )}

        <Alert
          message="下载链接"
          description={
            <>
              {downloadLink ? (
                <Link href={downloadLink} target="_blank" onClick={handleLinkClick}>
                  {downloadLink}
                </Link>
              ) : (
                <Text type="secondary">加载中...</Text>
              )}
            </>
          }
          type="info"
          showIcon
        />

        <Paragraph>
          <Text strong>若使用浏览器下载缓慢，推荐使用迅雷下载</Text>
        </Paragraph>

        <Paragraph>
          <Text strong>注意：</Text>{" "}
          点击"离线安装"按钮后，将弹出文件选择对话框，请选择下载好的组件压缩包。（无需解压）
        </Paragraph>

        <div
          style={{ display: "flex", justifyContent: "flex-end", marginTop: 16 }}
        >
          <Button
            onClick={onClose}
            disabled={installing}
            style={{ marginRight: 8 }}
          >
            取消
          </Button>
          <Button type="primary" onClick={handleInstall} loading={installing}>
            离线安装
          </Button>
        </div>
      </Space>
    </Modal>
  );
};

export default OfflineInstallDialog;
