import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, Button, List, Typography, Space, Divider, message } from 'antd';
import { DeleteOutlined, ReloadOutlined } from '@ant-design/icons';
import { GetAppDataInfo, ClearAppData } from '../../wailsjs/go/main/App';
import { main } from '../../wailsjs/go/models';
import { formatFileSize } from '../utils/format';

const { Text } = Typography;

interface ClearDataDialogProps {
  visible: boolean;
  onClose: () => void;
}

const ClearDataDialog: React.FC<ClearDataDialogProps> = ({ visible, onClose }) => {
  const [dataInfo, setDataInfo] = useState<main.AppDataInfo | null>(null);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 加载数据信息
  const loadDataInfo = async () => {
    try {
      setRefreshing(true);
      const info = await GetAppDataInfo();
      setDataInfo(info);
    } catch (error) {
      message.error(`获取数据信息失败: ${error}`);
    } finally {
      setRefreshing(false);
    }
  };

  // 初始加载和对话框打开时加载数据
  useEffect(() => {
    if (visible) {
      loadDataInfo();
    } else {
      // 关闭对话框时清空选择
      setSelectedTypes([]);
    }
  }, [visible]);

  // 处理选择变化
  const handleCheckboxChange = (dataType: string) => {
    setSelectedTypes(prev => {
      if (prev.includes(dataType)) {
        return prev.filter(type => type !== dataType);
      } else {
        return [...prev, dataType];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (!dataInfo) return;
    
    if (selectedTypes.length === 5) {
      // 如果已全选，则取消全选
      setSelectedTypes([]);
    } else {
      // 否则全选
      setSelectedTypes(['logs', 'browser', 'config', 'database', 'components']);
    }
  };

  // 清除数据
  const handleClearData = async () => {
    if (selectedTypes.length === 0) {
      message.warning('请至少选择一种数据类型');
      return;
    }

    try {
      setLoading(true);
      const result = await ClearAppData(selectedTypes);
      
      if (result.errors && (result.errors as string[]).length > 0) {
        message.warning(`部分数据清除失败: ${(result.errors as string[]).join(', ')}`);
      } else {
        message.success('数据清除成功');
      }
      
      // 更新数据信息
      if (result.dataInfo) {
        setDataInfo(result.dataInfo as main.AppDataInfo);
      }
      
      // 清空选择
      setSelectedTypes([]);
    } catch (error) {
      message.error(`清除数据失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="清除应用数据"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ marginBottom: 16 }}>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadDataInfo} 
            loading={refreshing}
            size="small"
          >
            刷新数据大小
          </Button>
          <Text style={{ marginLeft: 16 }}>
            总大小: {dataInfo ? formatFileSize(dataInfo.totalSize) : '计算中...'}
          </Text>
        </div>

        <List
          bordered
          dataSource={[
            { key: 'logs', data: dataInfo?.logs },
            { key: 'browser', data: dataInfo?.browser },
            { key: 'config', data: dataInfo?.config },
            { key: 'database', data: dataInfo?.database },
            { key: 'components', data: { name: '组件文件', path: '', size: dataInfo?.components?.size || 0 } },
          ]}
          renderItem={item => (
            <List.Item>
              <Checkbox
                checked={selectedTypes.includes(item.key)}
                onChange={() => handleCheckboxChange(item.key)}
                disabled={loading}
              >
                <Space>
                  <Text>{item.data?.name}</Text>
                  <Text type="secondary">({formatFileSize(item.data?.size || 0)})</Text>
                </Space>
              </Checkbox>
            </List.Item>
          )}
        />

        <Divider style={{ margin: '12px 0' }} />

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Checkbox
            checked={selectedTypes.length === 5}
            indeterminate={selectedTypes.length > 0 && selectedTypes.length < 5}
            onChange={handleSelectAll}
            disabled={loading}
          >
            全选
          </Checkbox>
          
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              onClick={handleClearData}
              loading={loading}
              disabled={selectedTypes.length === 0}
            >
              清除选中数据
            </Button>
          </Space>
        </div>
      </Space>
    </Modal>
  );
};

export default ClearDataDialog;
