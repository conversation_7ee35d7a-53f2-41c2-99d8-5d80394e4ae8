import "./App.css";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import LoadingSpinner from "./components/LoadingSpinner";
import { useAuth } from "./contexts/AuthContext";
import Login from "./pages/Login";
import Home from "./pages/Home";
import ProtectedRoute from "./components/ProtectedRoute";
import AboutDialog from "./components/AboutDialog";
import ClearDataDialog from "./components/ClearDataDialog";
import RechargeDialog from './components/RechargeDialog';
import PlaywrightInstallDialog from './components/PlaywrightInstallDialog';
import OfflineInstallDialog from './components/OfflineInstallDialog';
import UpdateDialog from './components/UpdateDialog';
import { useState, useEffect } from "react";
import { LogInfo } from "../wailsjs/runtime/runtime";
import { GetVersion } from "../wailsjs/go/main/App";
import { CheckVersion, } from "../wailsjs/go/main/APIService";
import { Modal } from "antd";

function App() {
  const { isAuthenticated } = useAuth();
  const [aboutDialogVisible, setAboutDialogVisible] = useState(false);
  const [clearDataDialogVisible, setClearDataDialogVisible] = useState(false);
  const [isRechargeDialogOpen, setIsRechargeDialogOpen] = useState(false);
  const [isPlaywrightInstallDialogOpen, setIsPlaywrightInstallDialogOpen] = useState(false);
  const [isOfflineInstallDialogOpen, setIsOfflineInstallDialogOpen] = useState(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [currentVersion, setCurrentVersion] = useState("");
  const [versionInfo, setVersionInfo] = useState({
    version: "",
    downloadUrl: "",
    forceUpdate: false,
    updateLog: ""
  });

  // 监听关于对话框事件
  useEffect(() => {
    const handleShowAbout = () => {
      LogInfo("显示关于对话框");
      setAboutDialogVisible(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:about", handleShowAbout);

    return () => {
      window.runtime.EventsOff("show:about", handleShowAbout);
    };
  }, []);

  // 监听清除数据对话框事件
  useEffect(() => {
    const handleShowClearData = () => {
      LogInfo("显示清除数据对话框");
      setClearDataDialogVisible(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:clearData", handleShowClearData);

    return () => {
      window.runtime.EventsOff("show:clearData", handleShowClearData);
    };
  }, []);

  // 修改为使用useEffect和事件监听
  useEffect(() => {
    const handleShowRecharge = () => {
      LogInfo("显示充值对话框");
      setIsRechargeDialogOpen(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:recharge", handleShowRecharge);

    return () => {
      window.runtime.EventsOff("show:recharge", handleShowRecharge);
    };
  }, []);

  // 监听Playwright安装对话框事件
  useEffect(() => {
    const handleShowPlaywrightInstall = () => {
      LogInfo("显示组件安装对话框");
      setIsPlaywrightInstallDialogOpen(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:playwrightInstall", handleShowPlaywrightInstall);

    return () => {
      window.runtime.EventsOff("show:playwrightInstall", handleShowPlaywrightInstall);
    };
  }, []);

  // 监听离线安装组件对话框事件
  useEffect(() => {
    const handleShowOfflineInstall = () => {
      LogInfo("显示离线安装组件对话框");
      setIsOfflineInstallDialogOpen(true);
    };
    // 注册事件监听器
    window.runtime.EventsOn("show:offlineInstall", handleShowOfflineInstall);
    return () => {
      window.runtime.EventsOff("show:offlineInstall", handleShowOfflineInstall);
    };
  }, []);

  // 获取当前版本
  useEffect(() => {
    const fetchCurrentVersion = async () => {
      try {
        const version = await GetVersion();
        setCurrentVersion(version);
      } catch (error) {
        console.error("获取当前版本失败:", error);
      }
    };
    fetchCurrentVersion();
  }, []);

  // 监听检查更新对话框事件
  useEffect(() => {
    const handleShowCheckUpdate = async () => {
      LogInfo("显示检查更新对话框");
      try {
        const versionData = await CheckVersion();
        setVersionInfo({
          version: versionData.version,
          downloadUrl: versionData.download_url,
          forceUpdate: versionData.force_update,
          updateLog: versionData.update_log
        });
        // 比较版本号，如果相同则提示已是最新版本
        if (versionData.version === currentVersion) {
          // 使用Modal.info显示提示信息
          Modal.info({
            title: '检查更新',
            content: '你的软件已是最新版本',
            okText: '确定',
            centered: true
          });
        } else {
          // 版本不同，显示更新对话框
          setIsUpdateDialogOpen(true);
        }
      } catch (error) {
        console.error("检查更新失败:", error);
      }
    };
    // 注册事件监听器
    window.runtime.EventsOn("show:checkUpdate", handleShowCheckUpdate);
    return () => {
      window.runtime.EventsOff("show:checkUpdate", handleShowCheckUpdate);
    };
  }, [currentVersion]);

  return (
    <BrowserRouter>
      <div id="App">
        {/* Global loading spinner */}
        <LoadingSpinner />

        <Routes>
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <ProtectedRoute>
                  <Home />
                </ProtectedRoute>
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
          <Route
            path="/login"
            element={isAuthenticated ? <Navigate to="/" replace /> : <Login />}
          />
        </Routes>
        {/* 全局对话框 */}
        <AboutDialog visible={aboutDialogVisible} onClose={() => setAboutDialogVisible(false)} />
        <ClearDataDialog visible={clearDataDialogVisible} onClose={() => setClearDataDialogVisible(false)} />
        <RechargeDialog
          isOpen={isRechargeDialogOpen}
          onClose={() => setIsRechargeDialogOpen(false)}
        />
        <PlaywrightInstallDialog
          visible={isPlaywrightInstallDialogOpen}
          onClose={() => setIsPlaywrightInstallDialogOpen(false)}
        />
        <OfflineInstallDialog
          visible={isOfflineInstallDialogOpen}
          onClose={() => setIsOfflineInstallDialogOpen(false)}
        />
        <UpdateDialog
          visible={isUpdateDialogOpen}
          onClose={() => setIsUpdateDialogOpen(false)}
          versionInfo={versionInfo}
          currentVersion={currentVersion}
        />
      </div>
    </BrowserRouter>
  );
}

export default App;
