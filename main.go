package main

import (
	"context"
	"embed"
	"fmt"
	"os"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
	"github.com/wailsapp/wails/v2/pkg/options/mac"
	"github.com/wailsapp/wails/v2/pkg/options/windows"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// 创建一个初始上下文用于显示消息对话框
	initialCtx := context.Background()

	// 检查是否是单实例运行
	isFirstInstance, err := checkSingleInstance()
	if err != nil {
		fmt.Printf("单实例检查出错: %v\n", err)

		// 显示错误对话框
		rt.MessageDialog(initialCtx, rt.MessageDialogOptions{
			Type:    rt.ErrorDialog,
			Title:   "启动错误",
			Message: fmt.Sprintf("单实例检查出错: %v", err),
		})

		os.Exit(1)
	}

	// 如果不是第一个实例，显示提示并退出
	if !isFirstInstance {
		fmt.Println("检测到另一个实例正在运行，退出程序")

		// 显示提示对话框
		rt.MessageDialog(initialCtx, rt.MessageDialogOptions{
			Type:    rt.InfoDialog,
			Title:   "应用程序已在运行",
			Message: "山竹阅卷已经在运行中，请查看任务栏或应用程序窗口。",
		})

		os.Exit(0)
	}

	// 确保在程序退出时释放锁
	defer releaseSingleInstanceLock()

	// Initialize configuration
	initConfig()

	// Initialize database
	_, err = InitDB() // 初始化全局数据库连接
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize database: %v", err))
	}

	// Create instances of services
	app := NewApp()
	apiService := NewAPIService()

	// Create application menu
	appMenu := CreateAppMenu(app)
	// Create application with options
	err = wails.Run(&options.App{
		Title:       "山竹阅卷",
		MinWidth:    630,
		Width:       630,
		MinHeight:   760,
		Height:      760,
		AlwaysOnTop: true,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		// DisableResize: true, // 禁用窗口大小调整
		Menu: appMenu,
		Mac: &mac.Options{
			TitleBar: mac.TitleBarDefault(),
			About: &mac.AboutInfo{
				Title:   "山竹阅卷",
				Message: "© 2025 ShanZhuLab. All rights reserved.",
				Icon:    nil,
			},
		},
		Windows: &windows.Options{
			WebviewIsTransparent: true,
			BackdropType:         windows.Acrylic,
			WindowIsTranslucent:  false,
			DisableWindowIcon:    false,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup: func(ctx context.Context) {
			// 初始化日志器
			InitLogger(ctx)

			// 初始化应用和服务
			app.startup(ctx)
			apiService.Startup(ctx)

			if AppLogger != nil {
				AppLogger.Info("应用启动完成")
			}
		},
		OnShutdown: func(ctx context.Context) {
			// 先关闭应用
			app.shutdown(ctx)

			// 释放单实例锁
			releaseSingleInstanceLock()

			// 最后关闭日志器
			if AppLogger != nil {
				AppLogger.Info("应用正在关闭")
				AppLogger.Close()
			}
		},
		Bind: []any{
			app,
			apiService,
		},
	})
	if err != nil {
		println("Error:", err.Error())
	}
}
