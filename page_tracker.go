package main

import (
	"log"
	"sync"

	"github.com/playwright-community/playwright-go"
)

type PageTracker struct {
	targetPage playwright.Page
	mu         sync.RWMutex
}

// 设置当前页面
func (pt *PageTracker) SetTargetPage(page playwright.Page) {
	pt.mu.Lock()
	defer pt.mu.Unlock()
	pt.targetPage = page
}

// 获取当前页面
func (pt *PageTracker) GetTargetPage() playwright.Page {
	pt.mu.RLock()
	defer pt.mu.RUnlock()
	return pt.targetPage
}

func (pt *PageTracker) toastMessage() {
	// 定义提醒内容和持续时间（毫秒）
	toastMessage := "🚀 请在当前页面进行阅卷操作!"
	toastDuration := 10000 // 10秒

	// 使用 page.Evaluate 执行一段复杂的 JS 来创建并显示一个 Toast 通知
	_, err := pt.GetTargetPage().Evaluate(`(args) => {
		const { message, duration } = args;

		// 创建一个 div 元素作为提醒框
		const toast = document.createElement('div');
		toast.textContent = message;

		// 设置样式
		toast.style.position = 'fixed';
		toast.style.bottom = '20px';
		toast.style.left = '50%';
		toast.style.transform = 'translateX(-50%)';
		toast.style.backgroundColor = '#333';
		toast.style.color = 'white';
		toast.style.padding = '12px 20px';
		toast.style.borderRadius = '8px';
		toast.style.zIndex = '9999';
		toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
		toast.style.transition = 'opacity 0.5s ease';
		
		// 添加到页面中
		document.body.appendChild(toast);

		// 在指定时间后淡出并移除
		setTimeout(() => {
			toast.style.opacity = '0';
			setTimeout(() => {
				toast.remove();
			}, 500); // 等待淡出动画完成再移除
		}, duration);

	}`, map[string]interface{}{
		"message":  toastMessage,
		"duration": toastDuration,
	})

	if err != nil {
		log.Fatalf("could not create custom toast: %v", err)
	}
}
