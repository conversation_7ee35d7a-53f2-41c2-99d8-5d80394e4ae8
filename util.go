package main

import (
	"archive/zip"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/unidoc/unioffice/document"
)

// 将阅卷记录转成markdown格式的字符串,用于AI分析
func GradingRecordsToMarkdown(records []GradingRecordListItem) string {
	if len(records) == 0 {
		return "没有找到阅卷记录"
	}
	// 添加标题
	var builder strings.Builder
	builder.WriteString("# 阅卷记录汇总\n\n")
	builder.WriteString("生成时间: " + time.Now().Format("2006-01-02 15:04:05") + "\n\n")
	builder.WriteString("共 " + fmt.Sprintf("%d", len(records)) + " 条记录\n\n")
	// 检查是否是同一题,如果是就添加评分标准头
	firstCriteria := records[0].GradingCriteria
	for _, record := range records {
		if record.GradingCriteria != firstCriteria {
			firstCriteria = ""
			break
		}
	}
	if firstCriteria != "" {
		builder.WriteString("## 评分标准\n\n")
		builder.WriteString("```\n" + firstCriteria + "\n```\n\n")
	}
	// 添加表格头部
	// 只包含阅卷记录的创建时间、用户、分数、学生答案和得分细节
	builder.WriteString("| 阅卷时间 | 阅卷人账号 | 得分 | 学生答案 | 得分细节 |\n")
	builder.WriteString("|---------|----------|-----|---------|----------|\n")
	// 添加表格内容
	for _, record := range records {
		createdAt := record.CreatedAt.Format("2006-01-02 15:04:05")
		builder.WriteString(fmt.Sprintf("| %s | %s | %.1f | %s | %s |\n",
			createdAt,
			record.UserEmail,
			record.Score,
			record.AnswerText,
			record.ScoreDetails,
		))
	}
	return builder.String()
}

// 读取并解析CSV文件
func ReadCSVFile(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		return ""
	}
	defer file.Close()
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return ""
	}
	var builder strings.Builder
	// 添加标题行
	if len(records) > 0 {
		builder.WriteString("文档中包含以下数据表格：\n\n")
		builder.WriteString("| " + strings.Join(records[0], " | ") + " |\n")
		builder.WriteString("|" + strings.Repeat("---|", len(records[0])) + "\n")
	}
	// 添加数据行
	for i, row := range records {
		if i == 0 {
			continue // 跳过标题行
		}
		builder.WriteString("| " + strings.Join(row, " | ") + " |\n")
	}
	return builder.String()
}

// 读取Word文档内容
func ReadWordDocument(filePath string) (string, error) {
	doc, err := document.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("无法打开Word文档: %v", err)
	}
	var content strings.Builder
	for _, para := range doc.Paragraphs() {
		for _, run := range para.Runs() {
			content.WriteString(run.Text())
		}
		content.WriteString("\n") // 段落间添加换行
	}
	return content.String(), nil
}

// UnzipFile 解压ZIP文件到指定目录
func UnzipFile(zipPath string, destPath string) error {
	// 打开ZIP文件
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return fmt.Errorf("无法打开ZIP文件: %v", err)
	}
	defer r.Close()
	// 检查压缩包是否包含必要的文件夹
	hasPlaywright := false
	hasPlaywrightGo := false
	for _, f := range r.File {
		if strings.HasPrefix(f.Name, "ms-playwright/") {
			hasPlaywright = true
		}
		if strings.HasPrefix(f.Name, "ms-playwright-go/") {
			hasPlaywrightGo = true
		}
	}
	// 如果缺少必要的文件夹，返回错误
	if !hasPlaywright || !hasPlaywrightGo {
		return fmt.Errorf("压缩包不合法：必须包含 ms-playwright 和 ms-playwright-go 文件夹")
	}
	// 检查目标目录是否已存在这些文件夹，如果存在则重命名为备份
	playwrightPath := filepath.Join(destPath, "ms-playwright")
	playwrightGoPath := filepath.Join(destPath, "ms-playwright-go")
	// 生成时间戳作为备份文件夹的后缀
	timeStamp := time.Now().Format("20060102150405")
	// 检查并备份 ms-playwright 文件夹
	if _, err := os.Stat(playwrightPath); err == nil {
		backupPath := playwrightPath + ".backup." + timeStamp
		if err := os.Rename(playwrightPath, backupPath); err != nil {
			return fmt.Errorf("备份已存在的 ms-playwright 文件夹失败: %v", err)
		}
	}
	// 检查并备份 ms-playwright-go 文件夹
	if _, err := os.Stat(playwrightGoPath); err == nil {
		backupPath := playwrightGoPath + ".backup." + timeStamp
		if err := os.Rename(playwrightGoPath, backupPath); err != nil {
			return fmt.Errorf("备份已存在的 ms-playwright-go 文件夹失败: %v", err)
		}
	}
	// 遍历ZIP文件中的所有文件
	for _, f := range r.File {
		// 构建目标文件路径
		fpath := filepath.Join(destPath, f.Name)
		// 检查路径是否在目标目录内（防止目录穿越攻击）
		if !strings.HasPrefix(fpath, filepath.Clean(destPath)+string(os.PathSeparator)) {
			return fmt.Errorf("非法的文件路径: %s", f.Name)
		}
		if f.FileInfo().IsDir() {
			// 创建目录
			os.MkdirAll(fpath, os.ModePerm)
			continue
		}
		// 确保父目录存在
		if err := os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
			return fmt.Errorf("创建目录失败: %v", err)
		}
		// 创建目标文件
		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return fmt.Errorf("创建文件失败: %v", err)
		}
		// 打开ZIP中的源文件
		src, err := f.Open()
		if err != nil {
			outFile.Close()
			return fmt.Errorf("打开ZIP中的文件失败: %v", err)
		}
		// 复制文件内容
		_, err = io.Copy(outFile, src)
		src.Close()
		outFile.Close()
		if err != nil {
			return fmt.Errorf("复制文件内容失败: %v", err)
		}
	}
	return nil
}
